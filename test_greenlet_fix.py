#!/usr/bin/env python3
"""
Test script to verify the greenlet fix for add to library functionality.
"""

import asyncio
import httpx
import json

async def test_add_to_library():
    """Test adding manga to library to verify greenlet fix."""
    
    # First, let's search for a manga
    search_url = "http://localhost:8000/api/v1/manga/search"
    search_params = {
        "query": "one piece",
        "provider": "mangadx",  # Use MangaDX as it's likely to work
        "page": 1,
        "limit": 5
    }
    
    print("🔍 Searching for manga...")
    async with httpx.AsyncClient() as client:
        try:
            # Search for manga
            search_response = await client.get(search_url, params=search_params)
            print(f"Search status: {search_response.status_code}")
            
            if search_response.status_code != 200:
                print(f"Search failed: {search_response.text}")
                return
            
            search_data = search_response.json()
            print(f"Found {len(search_data.get('results', []))} manga")
            
            if not search_data.get('results'):
                print("No manga found in search results")
                return
            
            # Get the first manga
            first_manga = search_data['results'][0]
            manga_id = first_manga['id']
            provider = first_manga['provider']
            
            print(f"📚 Testing with manga: {first_manga['title']} (ID: {manga_id}, Provider: {provider})")
            
            # Try to add to library
            add_url = "http://localhost:8000/api/v1/manga/add-to-library"
            add_data = {
                "provider": provider,
                "external_id": manga_id
            }
            
            print("➕ Adding manga to library...")
            add_response = await client.post(add_url, json=add_data)
            print(f"Add to library status: {add_response.status_code}")
            
            if add_response.status_code == 200:
                print("✅ SUCCESS! Manga added to library without greenlet error!")
                response_data = add_response.json()
                print(f"Created manga with ID: {response_data.get('id')}")
            else:
                print(f"❌ FAILED: {add_response.text}")
                
        except Exception as e:
            print(f"❌ Error during test: {e}")

if __name__ == "__main__":
    asyncio.run(test_add_to_library())
